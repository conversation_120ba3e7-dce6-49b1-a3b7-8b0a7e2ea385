import React from 'react';

interface Program {
  title: string;
  level: string;
  duration: string;
  qualifications: string[];
  fees: string;
  description: string;
  icon: string;
}

const Programs: React.FC = () => {
  const programs: Program[] = [
    {
      title: "Nursing and Midwifery (Upgrading)",
      level: "Ordinary Diploma",
      duration: "2 Years",
      qualifications: [
        "Certificate in Nursing and Midwifery",
        "Form IV Certificate with relevant subjects",
        "Minimum 2 years work experience"
      ],
      fees: "Contact for current fees",
      description: "Advanced training program for practicing nurses seeking to upgrade their qualifications to diploma level.",
      icon: "👩‍⚕️"
    },
    {
      title: "Nursing and Midwifery",
      level: "Ordinary Diploma",
      duration: "3 Years",
      qualifications: [
        "Form IV Certificate with Division I-III",
        "Passes in Biology, Chemistry, Physics, English, and Mathematics",
        "Age 18-25 years"
      ],
      fees: "Contact for current fees",
      description: "Comprehensive program preparing students for professional nursing and midwifery practice.",
      icon: "🏥"
    },
    {
      title: "Pharmaceutical Sciences",
      level: "Certificate & Diploma",
      duration: "1-3 Years",
      qualifications: [
        "Form IV Certificate for Diploma",
        "Form II Certificate for Certificate level",
        "Relevant science subjects"
      ],
      fees: "Contact for current fees",
      description: "Training in pharmaceutical sciences, drug dispensing, and pharmacy management.",
      icon: "💊"
    }
  ];

  return (
    <section id="programs" className="py-16 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Programs Offered
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Choose from our comprehensive range of health sciences programs designed to prepare you for a successful career in healthcare.
          </p>
          <div className="w-24 h-1 bg-blue-600 mx-auto mt-4"></div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {programs.map((program, index) => (
            <div key={index} className="card group hover:scale-105 transform transition-all duration-300">
              {/* Program Icon */}
              <div className="text-center mb-6">
                <div className="text-4xl mb-4">{program.icon}</div>
                <div className="inline-block bg-blue-100 text-blue-800 text-xs font-semibold px-3 py-1 rounded-full">
                  {program.level}
                </div>
              </div>

              {/* Program Title */}
              <h3 className="text-xl font-bold text-gray-900 mb-3 text-center">
                {program.title}
              </h3>

              {/* Program Description */}
              <p className="text-gray-600 mb-4 text-center">
                {program.description}
              </p>

              {/* Duration */}
              <div className="flex items-center justify-center mb-4">
                <svg className="w-5 h-5 text-blue-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                </svg>
                <span className="text-gray-700 font-medium">{program.duration}</span>
              </div>

              {/* Qualifications */}
              <div className="mb-4">
                <h4 className="font-semibold text-gray-900 mb-2">Entry Requirements:</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  {program.qualifications.map((qualification, idx) => (
                    <li key={idx} className="flex items-start">
                      <svg className="w-4 h-4 text-blue-600 mr-2 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      {qualification}
                    </li>
                  ))}
                </ul>
              </div>

              {/* Fees */}
              <div className="mb-6">
                <div className="flex items-center justify-between">
                  <span className="font-semibold text-gray-900">Fees:</span>
                  <span className="text-blue-600 font-bold">{program.fees}</span>
                </div>
              </div>

              {/* Apply Button */}
              <div className="text-center">
                <button className="btn-primary w-full group-hover:bg-blue-700">
                  Apply Now
                </button>
              </div>
            </div>
          ))}
        </div>

        {/* Additional Programs Note */}
        <div className="mt-12 text-center">
          <div className="bg-gray-50 rounded-lg p-8">
            <h3 className="text-xl font-bold text-gray-900 mb-4">Additional Programs Available</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
              <div className="bg-white rounded-lg p-4 shadow-sm">
                <h4 className="font-semibold text-blue-600">Clinical Medicine (CO)</h4>
                <p className="text-gray-600">Certificate & Diploma levels</p>
              </div>
              <div className="bg-white rounded-lg p-4 shadow-sm">
                <h4 className="font-semibold text-blue-600">Community Development</h4>
                <p className="text-gray-600">Professional training program</p>
              </div>
              <div className="bg-white rounded-lg p-4 shadow-sm">
                <h4 className="font-semibold text-blue-600">Medical Laboratory Sciences</h4>
                <p className="text-gray-600">Laboratory technology training</p>
              </div>
              <div className="bg-white rounded-lg p-4 shadow-sm">
                <h4 className="font-semibold text-blue-600">Social Work</h4>
                <p className="text-gray-600">Community service preparation</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Programs;
