import React, { useState, useEffect } from 'react';

interface Announcement {
  id: number;
  title: string;
  date: string;
  content: string;
  type: 'admission' | 'event' | 'general' | 'urgent';
  isNew: boolean;
}

const Announcements: React.FC = () => {
  const [currentIndex, setCurrentIndex] = useState(0);

  const announcements: Announcement[] = [
    {
      id: 1,
      title: "Applications Open for 2024/2025 Academic Year",
      date: "2024-08-01",
      content: "We are now accepting applications for all diploma and certificate programs. Early application is encouraged as spaces are limited.",
      type: "admission",
      isNew: true
    },
    {
      id: 2,
      title: "New Laboratory Equipment Installation",
      date: "2024-07-25",
      content: "State-of-the-art laboratory equipment has been installed to enhance practical learning experience for our students.",
      type: "general",
      isNew: true
    },
    {
      id: 3,
      title: "Graduation Ceremony 2024",
      date: "2024-07-15",
      content: "Join us in celebrating our graduating class of 2024. The ceremony will be held at the main campus auditorium.",
      type: "event",
      isNew: false
    },
    {
      id: 4,
      title: "Clinical Placement Opportunities",
      date: "2024-07-10",
      content: "New partnerships with regional hospitals provide expanded clinical placement opportunities for nursing students.",
      type: "general",
      isNew: false
    },
    {
      id: 5,
      title: "Scholarship Program Available",
      date: "2024-07-05",
      content: "Merit-based scholarships are available for outstanding students. Application deadline is August 30th, 2024.",
      type: "admission",
      isNew: false
    }
  ];

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % announcements.length);
    }, 4000);

    return () => clearInterval(timer);
  }, [announcements.length]);

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'admission':
        return 'bg-blue-100 text-blue-800';
      case 'event':
        return 'bg-green-100 text-green-800';
      case 'urgent':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'admission':
        return '📚';
      case 'event':
        return '🎓';
      case 'urgent':
        return '⚠️';
      default:
        return '📢';
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <section id="announcements" className="py-16 bg-blue-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Latest Announcements
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Stay updated with the latest news, events, and important information from MKOLANI Foundation.
          </p>
          <div className="w-24 h-1 bg-blue-600 mx-auto mt-4"></div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Featured Announcement - Scrolling */}
          <div className="bg-white rounded-lg shadow-lg overflow-hidden">
            <div className="bg-blue-600 text-white px-6 py-4">
              <h3 className="text-xl font-bold flex items-center">
                <svg className="w-6 h-6 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                </svg>
                Featured Announcement
              </h3>
            </div>
            
            <div className="p-6 h-64 flex flex-col justify-between">
              <div>
                <div className="flex items-center justify-between mb-4">
                  <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${getTypeColor(announcements[currentIndex].type)}`}>
                    <span className="mr-1">{getTypeIcon(announcements[currentIndex].type)}</span>
                    {announcements[currentIndex].type.charAt(0).toUpperCase() + announcements[currentIndex].type.slice(1)}
                  </span>
                  {announcements[currentIndex].isNew && (
                    <span className="bg-red-500 text-white text-xs font-bold px-2 py-1 rounded-full animate-pulse">
                      NEW
                    </span>
                  )}
                </div>
                
                <h4 className="text-lg font-bold text-gray-900 mb-3">
                  {announcements[currentIndex].title}
                </h4>
                
                <p className="text-gray-600 mb-4">
                  {announcements[currentIndex].content}
                </p>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-500">
                  {formatDate(announcements[currentIndex].date)}
                </span>
                <button className="text-blue-600 hover:text-blue-700 font-medium text-sm">
                  Read More →
                </button>
              </div>
            </div>

            {/* Progress Indicators */}
            <div className="bg-gray-50 px-6 py-3">
              <div className="flex space-x-2 justify-center">
                {announcements.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => setCurrentIndex(index)}
                    className={`w-2 h-2 rounded-full transition-colors duration-200 ${
                      index === currentIndex ? 'bg-blue-600' : 'bg-gray-300'
                    }`}
                  />
                ))}
              </div>
            </div>
          </div>

          {/* All Announcements List */}
          <div className="bg-white rounded-lg shadow-lg">
            <div className="bg-gray-50 px-6 py-4 border-b">
              <h3 className="text-xl font-bold text-gray-900">All Announcements</h3>
            </div>
            
            <div className="max-h-96 overflow-y-auto">
              {announcements.map((announcement, index) => (
                <div
                  key={announcement.id}
                  className={`p-4 border-b border-gray-100 hover:bg-gray-50 cursor-pointer transition-colors ${
                    index === currentIndex ? 'bg-blue-50 border-primary-200' : ''
                  }`}
                  onClick={() => setCurrentIndex(index)}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center mb-2">
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium mr-2 ${getTypeColor(announcement.type)}`}>
                          <span className="mr-1">{getTypeIcon(announcement.type)}</span>
                          {announcement.type.charAt(0).toUpperCase() + announcement.type.slice(1)}
                        </span>
                        {announcement.isNew && (
                          <span className="bg-red-500 text-white text-xs font-bold px-2 py-1 rounded-full">
                            NEW
                          </span>
                        )}
                      </div>
                      
                      <h4 className="font-semibold text-gray-900 mb-1">
                        {announcement.title}
                      </h4>
                      
                      <p className="text-sm text-gray-600 mb-2 line-clamp-2">
                        {announcement.content}
                      </p>
                      
                      <span className="text-xs text-gray-500">
                        {formatDate(announcement.date)}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Call to Action */}
        <div className="mt-12 text-center">
          <div className="bg-white rounded-lg shadow-md p-8">
            <h3 className="text-xl font-bold text-gray-900 mb-4">
              Stay Connected
            </h3>
            <p className="text-gray-600 mb-6">
              Don't miss important updates! Follow us on social media or contact us directly for the latest information.
            </p>
            <div className="space-x-4">
              <button className="btn-primary">
                Contact Us
              </button>
              <button className="btn-secondary">
                Subscribe to Updates
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Announcements;
